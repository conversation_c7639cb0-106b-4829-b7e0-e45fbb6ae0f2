import { createContext, ReactNode, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { Buoy, SensorData, GeoPoint, ControlResponse, WebSocketConnectionState, Subscription } from '../types';
import { getBuoys, getBuoyData, getBuoyLocations } from '../api';
import { ApiError, handleApplicationError } from '../utils/errorHandler';
import { useToast } from '../components/ui';
import webSocketClient from '../utils/websocket';

// 定义WebSocket消息类型 - 实际接收到的格式
interface WebSocketSensorMessage {
  id: string;
  buoyId: string;
  data_type: string;
  value: number | string | object;
  unit?: string;
  timestamp: string;
}

// 定义状态变化事件类型
interface StatusChangeEvent {
  id: string;
  buoyId: string;
  timestamp: string;
  status: string;
  previousStatus?: string;
  source: string;
  type: string;
}

// 定义上下文类型
interface BuoyContextType {
  buoys: Buoy[];
  selectedBuoyId: string | null;
  sensorData: SensorData[];
  locationHistory: GeoPoint[];
  // 状态事件历史记录
  statusEvents: StatusChangeEvent[];
  // 更细粒度的加载状态
  loadingBuoys: boolean;
  loadingSensorData: boolean;
  loadingLocationHistory: boolean;
  error: ApiError | null;
  setSelectedBuoyId: (id: string | null) => void;
  // WebSocket相关方法
  handleWebSocketMessage: (message: WebSocketSensorMessage | unknown) => void;
  // 控制响应相关
  controlResponse: ControlResponse | null;
  controlResponseLoading: boolean;
  // WebSocket连接状态
  wsConnectionState: WebSocketConnectionState;
  // WebSocket连接方法
  wsConnect: () => Promise<void>;
  // WebSocket断开连接方法
  wsDisconnect: () => Promise<void>;
  // 检查WebSocket是否已连接
  wsIsConnected: () => boolean;
  // 发送WebSocket消息
  wsSend: (topic: string, message: unknown) => void;
  // 订阅主题
  subscribe: (topic: string, callback: (message: unknown) => void) => Subscription;
  // 取消订阅
  unsubscribe: (subscription: Subscription) => void;
  // 历史轨迹显示控制
  showHistoryTrack: boolean;
  setShowHistoryTrack: (show: boolean) => void;
}

// 创建上下文
export const BuoyContext = createContext<BuoyContextType | undefined>(undefined);

// 上下文提供者组件
export const BuoyProvider = ({ children }: { children: ReactNode }) => {
  // 状态管理
  const [buoys, setBuoys] = useState<Buoy[]>([]);
  const [selectedBuoyId, setSelectedBuoyId] = useState<string | null>(null);
  const [sensorData, setSensorData] = useState<SensorData[]>([]);
  const [locationHistory, setLocationHistory] = useState<GeoPoint[]>([]);
  // 状态事件历史记录
  const [statusEvents, setStatusEvents] = useState<StatusChangeEvent[]>([]);
  // 用于防止重复处理状态消息的缓存
  const processedStatusMessages = useRef<Set<string>>(new Set());

  // 更细粒度的加载状态
  const [loadingBuoys, setLoadingBuoys] = useState<boolean>(true);
  const [loadingSensorData, setLoadingSensorData] = useState<boolean>(false);
  const [loadingLocationHistory, setLoadingLocationHistory] = useState<boolean>(false);

  // 控制响应状态
  const [controlResponse, setControlResponse] = useState<ControlResponse | null>(null);
  const [controlResponseLoading, setControlResponseLoading] = useState<boolean>(false);

  const [error, setError] = useState<ApiError | null>(null);
  
  // 历史轨迹显示控制
  const [showHistoryTrack, setShowHistoryTrack] = useState<boolean>(false);

  // 使用Toast
  const { showToast } = useToast();

  // WebSocket状态
  const [wsConnectionState, setWsConnectionState] = useState<WebSocketConnectionState>(
    webSocketClient.getConnectionState()
  );

  // 存储订阅的引用
  const subscriptionsRef = useRef<Subscription[]>([]);

  // 监听WebSocket连接状态变化
  useEffect(() => {
    const removeListener = webSocketClient.onConnectionStateChange((state) => {
      setWsConnectionState(state);
    });

    return () => {
      removeListener();
    };
  }, []);

  // WebSocket连接方法
  const wsConnect = useCallback(async (): Promise<void> => {
    try {
      // 如果已经连接，直接返回
      if (webSocketClient.isConnected()) {
        return;
      }

      // 设置认证令牌
      const token = localStorage.getItem('token');
      if (token) {
        webSocketClient.setAuthToken(token);
      }

      // 连接WebSocket
      await webSocketClient.connect();
      // 移除重复的Toast通知，由WebSocketConnectionToast组件统一处理
      console.log("TP1. WebSocket连接成功");
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      showToast('WebSocket连接失败', 'error');
      throw error;
    }
  }, [showToast]);

  // WebSocket断开连接方法
  const wsDisconnect = useCallback(async (): Promise<void> => {
    try {
      if (!webSocketClient.isConnected()) {
        return;
      }

      // 清理所有订阅
      subscriptionsRef.current.forEach(subscription => {
        webSocketClient.unsubscribe(subscription);
      });
      subscriptionsRef.current = [];

      // 断开连接
      await webSocketClient.disconnect();
      showToast('WebSocket连接已断开', 'info');
    } catch (error) {
      console.error('WebSocket断开连接失败:', error);
      showToast('WebSocket断开连接失败', 'error');
      throw error;
    }
  }, [showToast]);

  // 检查WebSocket是否已连接
  const wsIsConnected = useCallback((): boolean => {
    return webSocketClient.isConnected();
  }, []);

  // 发送WebSocket消息
  const wsSend = useCallback((topic: string, message: unknown): void => {
    if (!webSocketClient.isConnected()) {
      showToast('WebSocket未连接，无法发送消息', 'error');
      throw new Error('WebSocket未连接，无法发送消息');
    }

    try {
      webSocketClient.send(topic, message);
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      showToast('发送WebSocket消息失败', 'error');
      throw error;
    }
  }, [showToast]);

  // 订阅主题
  const subscribe = useCallback((topic: string, callback: (message: unknown) => void): Subscription => {
    try {
      const subscription = webSocketClient.subscribe(topic, callback);

      // 存储订阅引用
      subscriptionsRef.current.push(subscription);

      return subscription;
    } catch (error) {
      console.error(`订阅主题 ${topic} 失败:`, error);
      showToast(`订阅主题失败: ${topic}`, 'error');
      throw error;
    }
  }, [showToast]);

  // 取消订阅
  const unsubscribe = useCallback((subscription: Subscription): void => {
    try {
      webSocketClient.unsubscribe(subscription);

      // 从引用中移除订阅
      subscriptionsRef.current = subscriptionsRef.current.filter(sub => sub.id !== subscription.id);
    } catch (error) {
      console.error('取消订阅失败:', error);
      showToast('取消订阅失败', 'error');
    }
  }, [showToast]);



  // 获取所有浮标数据
  useEffect(() => {
    const fetchBuoys = async () => {
      try {
        setLoadingBuoys(true);
        const response = await getBuoys();

        if (response.data && response.data.length > 0) {
          setBuoys(response.data);
          // 默认选中第一个浮标
          setSelectedBuoyId(response.data[0].id);
        } else {
          // 处理空数据
          showToast('未发现任何浮标数据', 'info');
        }

        // 成功后清除错误
        setError(null);
      } catch (err) {
        const apiError = handleApplicationError(err);
        console.error('获取浮标数据失败:', apiError);
        setError(apiError);
        showToast(`获取浮标数据失败: ${apiError.message}`, 'error');
      } finally {
        setLoadingBuoys(false);
      }
    };

    fetchBuoys();
  }, [showToast]);

  // 获取选中浮标的传感器数据
  useEffect(() => {
    const fetchSensorData = async () => {
      if (!selectedBuoyId) {
        setSensorData([]);
        return;
      }

      try {
        setLoadingSensorData(true);
        const response = await getBuoyData(selectedBuoyId);
        setSensorData(response.data);
        // 成功后清除错误
        setError(null);
      } catch (err) {
        const apiError = handleApplicationError(err);
        console.error('获取传感器数据失败:', apiError);
        setError(apiError);
        showToast(`获取传感器数据失败: ${apiError.message}`, 'error');
      } finally {
        setLoadingSensorData(false);
      }
    };

    fetchSensorData();
  }, [selectedBuoyId, showToast]);

  // 获取选中浮标的位置历史
  useEffect(() => {
    const fetchLocationHistory = async () => {
      if (!selectedBuoyId) {
        setLocationHistory([]);
        return;
      }

      try {
        setLoadingLocationHistory(true);
        const response = await getBuoyLocations(selectedBuoyId);
        setLocationHistory(response.data);
        // 成功后清除错误
        setError(null);
      } catch (err) {
        const apiError = handleApplicationError(err);
        console.error('获取位置历史数据失败:', apiError);
        setError(apiError);
        showToast(`获取位置历史数据失败: ${apiError.message}`, 'error');
      } finally {
        setLoadingLocationHistory(false);
      }
    };

    fetchLocationHistory();
  }, [selectedBuoyId, showToast]);

  // 处理位置数据更新
  const handleLocationUpdate = useCallback((locationData: GeoPoint) => {
    // 验证数据
    if (!locationData) {
      console.error('无效的位置数据: 数据为空');
      return;
    }

    // 确保经度和纬度是数字
    const longitude = Number(locationData.longitude);
    const latitude = Number(locationData.latitude);

    if (isNaN(longitude) || isNaN(latitude)) {
      console.error('无效的位置数据: 经度或纬度不是数字', locationData);
      return;
    }

    // 添加时间戳（如果没有）
    const locationWithTimestamp: GeoPoint = {
      longitude,
      latitude,
      timestamp: locationData.timestamp || new Date().toISOString()
    };

    // 更新位置历史数据
    setLocationHistory(prevHistory => {
      // 添加新位置到历史记录
      const newHistory = [...prevHistory, locationWithTimestamp];

      // 按时间排序
      newHistory.sort((a, b) =>
        new Date(a.timestamp || 0).getTime() - new Date(b.timestamp || 0).getTime()
      );

      // 限制历史记录数量，只保留最近的100个位置点
      const maxHistoryPoints = 100;
      if (newHistory.length > maxHistoryPoints) {
        return newHistory.slice(newHistory.length - maxHistoryPoints);
      }

      return newHistory;
    });
  }, []);

  // 处理传感器数据更新
  const handleSensorDataUpdate = useCallback((sensorData: SensorData) => {
    // 验证数据
    if (!sensorData || !sensorData.data_type) {
      console.error('无效的传感器数据: 缺少data_type', sensorData);
      return;
    }

    // 确保有ID
    if (!sensorData.id) {
      console.error('无效的传感器数据: 缺少id', sensorData);
      return;
    }

    // 确保有值
    if (sensorData.value === undefined) {
      console.error('无效的传感器数据: 缺少value', sensorData);
      return;
    }

    // 确保有浮标ID
    const buoyId = sensorData.buoyId || (typeof sensorData.id === 'string' ? sensorData.id.split('-')[0] : null);
    if (!buoyId) {
      console.error('无效的传感器数据: 无法确定浮标ID', sensorData);
      return;
    }

    // 确保数据符合SensorData类型
    const normalizedSensorData: SensorData = {
      id: sensorData.id,
      buoyId: buoyId,
      data_type: sensorData.data_type,
      value: typeof sensorData.value === 'object' ? 0 : Number(sensorData.value), // 转换为数字
      unit: sensorData.unit || '',
      timestamp: sensorData.timestamp || new Date().toISOString()
    };

    // 阈值报警逻辑（仅本次数据更新弹一次toast）
    try {
      // 只对数值型传感器做阈值判断
      if (typeof normalizedSensorData.value === 'number' && !isNaN(normalizedSensorData.value)) {
        const thresholdsRaw = localStorage.getItem('sensorThresholds');
        if (thresholdsRaw) {
          const thresholds = JSON.parse(thresholdsRaw);
          const thresholdObj = thresholds?.[normalizedSensorData.data_type];
          if (thresholdObj && typeof thresholdObj === 'object') {
            const upper = thresholdObj.upper;
            const lower = thresholdObj.lower;
            if (
              upper !== undefined &&
              upper !== '' &&
              normalizedSensorData.value > Number(upper)
            ) {
              showToast(
                `传感器类型: ${normalizedSensorData.data_type}，当前值: ${normalizedSensorData.value}${normalizedSensorData.unit ? ' ' + normalizedSensorData.unit : ''}，超出上限: ${upper}`,
                'warning'
              );
            } else if (
              lower !== undefined &&
              lower !== '' &&
              normalizedSensorData.value < Number(lower)
            ) {
              showToast(
                `传感器类型: ${normalizedSensorData.data_type}，当前值: ${normalizedSensorData.value}${normalizedSensorData.unit ? ' ' + normalizedSensorData.unit : ''}，低于下限: ${lower}`,
                'warning'
              );
            }
          }
        }
      }
    } catch (e) {
      // 阈值解析异常不影响主流程
      console.error('阈值报警处理异常', e);
    }

    // console.log(`处理传感器数据(${sensorData.data_type}):`, normalizedSensorData);

    // 更新传感器数据
    setSensorData(prevData => {
      // 检查是否已存在相同ID的数据
      const existingIndex = prevData.findIndex(item => item.id === normalizedSensorData.id);

      if (existingIndex >= 0) {
        // 更新现有数据
        const newData = [...prevData];
        newData[existingIndex] = normalizedSensorData;
        return newData;
      } else {
        // 添加新数据
        const newData = [...prevData, normalizedSensorData];

        // 按时间排序
        newData.sort((a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );

        // 限制数据点数量
        const maxDataPoints = 100;
        if (newData.length > maxDataPoints) {
          // 按数据类型分组，每种类型保留最新的N个点
          const groupedData = new Map<string, SensorData[]>();

          newData.forEach(item => {
            if (!groupedData.has(item.data_type)) {
              groupedData.set(item.data_type, []);
            }
            groupedData.get(item.data_type)?.push(item);
          });

          // 对每种类型的数据进行处理
          let result: SensorData[] = [];
          groupedData.forEach(items => {
            // 按时间排序
            items.sort((a, b) =>
              new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            );
            // 只保留最新的N个点
            result = result.concat(items.slice(0, maxDataPoints / groupedData.size));
          });

          return result;
        }

        return newData;
      }
    });
  }, [showToast]);

  // 处理状态更新
  const handleStatusUpdate = useCallback((statusData: {
    buoyId?: string,
    status?: string,
    timestamp?: string,
    type?: string,
    source?: string,
    previous_value?: string
  }) => {
    const targetBuoyId = statusData.buoyId || selectedBuoyId;

    if (!targetBuoyId) {
      console.warn('状态更新缺少浮标ID');
      return;
    }

    // 创建消息唯一标识符，用于去重
    const messageId = `${targetBuoyId}-${statusData.type}-${statusData.status}-${statusData.timestamp}`;

    // 检查是否已经处理过这个消息
    if (processedStatusMessages.current.has(messageId)) {
      console.log('跳过重复的状态消息:', messageId);
      return;
    }

    // 标记消息为已处理
    processedStatusMessages.current.add(messageId);

    // 清理过期的消息ID（保留最近的100个）
    if (processedStatusMessages.current.size > 100) {
      const messageIds = Array.from(processedStatusMessages.current);
      processedStatusMessages.current.clear();
      messageIds.slice(-50).forEach(id => processedStatusMessages.current.add(id));
    }

    console.log('处理状态更新:', { targetBuoyId, status: statusData.status, type: statusData.type });

    // 更新浮标状态
    setBuoys(prevBuoys => {
      return prevBuoys.map(buoy => {
        // 确保ID类型一致的比较
        if (String(buoy.id) === String(targetBuoyId)) {
          const updatedBuoy = {
            ...buoy,
            last_heartbeat: statusData.timestamp || new Date().toISOString()
          };

          // 只有当状态字段存在时才更新状态
          if (statusData.status) {
            updatedBuoy.status = statusData.status as 'active' | 'inactive' | 'error';
            console.log(`更新浮标 ${buoy.id} 状态: ${buoy.status} -> ${statusData.status}`);
          }

          return updatedBuoy;
        }
        return buoy;
      });
    });

    // 添加状态事件到历史记录（只有状态变化或心跳事件才记录）
    if (statusData.type === 'status_change' || statusData.type === 'heartbeat') {
      const event: StatusChangeEvent = {
        id: `${targetBuoyId}-${Date.now()}`,
        buoyId: targetBuoyId,
        timestamp: statusData.timestamp || new Date().toISOString(),
        status: statusData.status || 'unknown',
        previousStatus: statusData.previous_value,
        source: statusData.source || 'unknown',
        type: statusData.type
      };

      setStatusEvents(prevEvents => {
        const newEvents = [event, ...prevEvents];
        // 限制事件数量，只保留最近的50个事件
        return newEvents.slice(0, 50);
      });
    }

    // 显示状态变化通知
    if (statusData.type === 'status_change' && statusData.status && statusData.previous_value !== statusData.status) {
      const statusText = statusData.status === 'active' ? '在线' : '离线';
      const sourceText = statusData.source === 'heartbeat_timeout' ? '心跳超时' :
                        statusData.source === 'mqtt_lwt' ? 'MQTT连接' :
                        statusData.source === 'heartbeat' ? '心跳' : '未知';

      showToast(`浮标 ${targetBuoyId} 状态变更为${statusText} (${sourceText})`,
                statusData.status === 'active' ? 'success' : 'warning');
    }
  }, [selectedBuoyId, showToast]);

  // 处理控制响应
  const handleControlResponse = useCallback((response: ControlResponse) => {
    // 更新控制响应状态
    setControlResponse(response);
    setControlResponseLoading(false);

    // 如果有当前设置，可以更新浮标状态
    if (response.current_settings) {
      console.log('更新浮标当前设置:', response.current_settings);
    }
  }, []);

  // WebSocket消息处理函数
  const handleWebSocketMessage = useCallback((message: unknown) => {
    try {
      // 打印原始消息，帮助调试
      // console.log('收到WebSocket原始消息:', message);

      // 确保消息格式正确
      if (!message) {
        console.error('无效的WebSocket消息: 消息为空');
        return;
      }

      // 尝试解析消息（如果是字符串）
      let parsedMessage: any = message;
      if (typeof message === 'string') {
        try {
          parsedMessage = JSON.parse(message);
          // console.log('已解析字符串消息:', parsedMessage);
        } catch (e) {
          console.error('无法解析消息字符串:', e);
          return;
        }
      }

      // 处理控制响应消息
      if (parsedMessage.type === 'control_response' || parsedMessage.type === 'command_response') {
        // console.log('收到控制响应消息:', parsedMessage);
        handleControlResponse(parsedMessage as ControlResponse);
        return;
      }

      // 检查消息格式
      if (!parsedMessage.buoyId) {
        // 尝试从消息中提取浮标ID
        if (parsedMessage.id && typeof parsedMessage.id === 'string') {
          parsedMessage.buoyId = parsedMessage.id.split('-')[0];
          // console.log('从ID中提取浮标ID:', parsedMessage.buoyId);
        } else {
          console.error('无效的WebSocket消息: 缺少浮标ID', parsedMessage);
          return;
        }
      }

      // 对于状态变化消息，处理所有浮标的消息（不仅仅是当前选中的）
      const isStatusMessage = parsedMessage.type === 'status_change' ||
                             parsedMessage.type === 'heartbeat' ||
                             parsedMessage.data_type === 'status' ||
                             parsedMessage.data_type === 'heartbeat';

      // 只有非状态消息才需要检查是否为当前选中浮标
      if (!isStatusMessage && String(parsedMessage.buoyId) !== String(selectedBuoyId)) {
        console.log(`跳过非当前浮标的消息: ${parsedMessage.buoyId} != ${selectedBuoyId}`);
        return;
      }

      // console.log('处理WebSocket消息:', parsedMessage);

      // 处理位置数据
      if (parsedMessage.data_type === 'location') {
        // 位置数据的特殊处理
        if (parsedMessage.value && typeof parsedMessage.value === 'object') {
          // 使用类型断言
          const locationValue = parsedMessage.value as { longitude: number | string, latitude: number | string };
          if ('longitude' in locationValue && 'latitude' in locationValue) {
            const longitude = Number(locationValue.longitude);
            const latitude = Number(locationValue.latitude);

            // 创建位置数据对象
            const locationData: GeoPoint = {
              longitude,
              latitude,
              timestamp: parsedMessage.timestamp
            };

            // 更新位置历史记录
            handleLocationUpdate(locationData);

            // 同步更新 buoys 数组中对应浮标的位置信息
            setBuoys(prevBuoys => {
              return prevBuoys.map(buoy => {
                if (String(buoy.id) === String(parsedMessage.buoyId)) {
                  // 更新浮标的位置信息
                  return {
                    ...buoy,
                    location: {
                      longitude,
                      latitude
                    }
                  };
                }
                return buoy;
              });
            });
          } else {
            console.error('无效的位置数据格式:', locationValue);
          }
        } else {
          console.error('位置数据缺少value对象:', parsedMessage);
        }
      }
      // 处理传感器数据
      else if (['temperature', 'ph', 'water_level', 'salinity', 'turbidity', 'dissolved_oxygen', 'flow_rate'].includes(parsedMessage.data_type)) {
        // console.log('更新传感器数据:', parsedMessage);
        handleSensorDataUpdate(parsedMessage as SensorData);
      }
      // 处理状态变化消息
      else if (parsedMessage.type === 'status_change' || parsedMessage.data_type === 'status') {
        // console.log('更新状态数据:', parsedMessage);
        handleStatusUpdate({
          buoyId: parsedMessage.buoyId,
          status: String(parsedMessage.value),
          timestamp: parsedMessage.timestamp,
          type: parsedMessage.type,
          source: parsedMessage.source,
          previous_value: parsedMessage.previous_value
        });
      }
      // 处理心跳消息
      else if (parsedMessage.type === 'heartbeat' || parsedMessage.data_type === 'heartbeat') {
        // console.log('更新心跳数据:', parsedMessage);
        handleStatusUpdate({
          buoyId: parsedMessage.buoyId,
          timestamp: parsedMessage.timestamp,
          type: parsedMessage.type,
          source: parsedMessage.source
        });
      }
      // 处理未知类型
      else {
        console.warn('未知的数据类型:', parsedMessage.data_type);
      }
    } catch (error) {
      console.error('处理WebSocket消息时出错:', error);
    }
  }, [selectedBuoyId, handleLocationUpdate, handleSensorDataUpdate, handleStatusUpdate, handleControlResponse]);

  // 自动连接WebSocket
  useEffect(() => {
    // 检查是否已认证
    if (!isAuthenticated()) {
      return;
    }

    // 连接WebSocket
    const connectWs = async () => {
      try {
        if (!wsIsConnected()) {
          await wsConnect();
          console.log('WebSocket自动连接成功');
        }
      } catch (error) {
        console.error('WebSocket自动连接失败:', error);
      }
    };

    connectWs();

    // 组件卸载时断开连接
    return () => {
      if (wsIsConnected()) {
        wsDisconnect().catch(error => {
          console.error('WebSocket断开连接失败:', error);
        });
      }
    };
  }, [wsConnect, wsDisconnect, wsIsConnected]);

  // WebSocket订阅管理
  useEffect(() => {
    if (!selectedBuoyId || !isAuthenticated() || !wsIsConnected()) {
      return;
    }

    // 构建订阅主题
    const topic = `/topic/buoys/${selectedBuoyId}/data`;
    console.log(`订阅浮标数据主题: ${topic}`);

    // 订阅主题
    const subscription = subscribe(topic, handleWebSocketMessage);

    // 订阅控制响应主题
    const controlTopic = `/topic/buoys/${selectedBuoyId}/control_response`;
    console.log(`订阅浮标控制响应主题: ${controlTopic}`);
    const controlSubscription = subscribe(controlTopic, handleWebSocketMessage);

    // 清理函数：取消订阅
    return () => {
      unsubscribe(subscription);
      unsubscribe(controlSubscription);
    };
  }, [selectedBuoyId, subscribe, unsubscribe, handleWebSocketMessage, wsIsConnected]);

  // 全局浮标状态变化订阅
  useEffect(() => {
    if (!isAuthenticated() || !wsIsConnected()) {
      return;
    }

    // 订阅全局浮标状态变化主题
    const globalStatusTopic = '/topic/buoys/status_changes';
    console.log(`订阅全局浮标状态变化主题: ${globalStatusTopic}`);

    const globalStatusSubscription = subscribe(globalStatusTopic, handleWebSocketMessage);

    // 清理函数：取消订阅
    return () => {
      unsubscribe(globalStatusSubscription);
    };
  }, [subscribe, unsubscribe, handleWebSocketMessage, wsIsConnected]);

  // 检查用户是否已认证
  function isAuthenticated(): boolean {
    return localStorage.getItem('token') !== null;
  }

  const value = {
    buoys,
    selectedBuoyId,
    sensorData,
    locationHistory,
    statusEvents,
    loadingBuoys,
    loadingSensorData,
    loadingLocationHistory,
    error,
    setSelectedBuoyId,
    handleWebSocketMessage,
    controlResponse,
    controlResponseLoading,
    // WebSocket相关
    wsConnectionState,
    wsConnect,
    wsDisconnect,
    wsIsConnected,
    wsSend,
    subscribe,
    unsubscribe,
    // 历史轨迹显示控制
    showHistoryTrack,
    setShowHistoryTrack
  };

  return <BuoyContext.Provider value={value}>{children}</BuoyContext.Provider>;
};

// 自定义Hook，用于在组件中使用上下文
export const useBuoyContext = () => {
  const context = useContext(BuoyContext);
  if (context === undefined) {
    throw new Error('useBuoyContext must be used within a BuoyProvider');
  }
  return context;
};